"""
Base classes and utilities for database queries.
Provides common patterns, caching mechanisms, and optimized query utilities.
"""

import asyncio
import hashlib
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple, Union

import asyncpg
from vups.logger import logger
from vups_server.sql.db_pool import get_connection


class CacheManager:
    """Enhanced caching manager with TTL support and automatic cleanup."""

    def __init__(self, default_ttl: int = 300, max_cache_size: int = 1000):
        self._cache: Dict[str, Tuple[Any, datetime]] = {}
        self._default_ttl = default_ttl
        self._max_cache_size = max_cache_size
        self._lock = asyncio.Lock()

    def _create_cache_key(self, *args, **kwargs) -> str:
        """Create a deterministic cache key from arguments."""
        key_data = f"{args}_{sorted(kwargs.items())}"
        return hashlib.md5(key_data.encode()).hexdigest()

    async def get(self, key: str) -> Optional[Any]:
        """Get cached value if it exists and is not expired."""
        async with self._lock:
            if key in self._cache:
                value, timestamp = self._cache[key]
                if (datetime.now() - timestamp).total_seconds() < self._default_ttl:
                    return value
                else:
                    # Remove expired entry
                    del self._cache[key]
            return None

    async def set(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """Set cached value with optional custom TTL."""
        async with self._lock:
            # Clean up if cache is too large
            if len(self._cache) >= self._max_cache_size:
                await self._cleanup_old_entries()

            self._cache[key] = (value, datetime.now())

    async def _cleanup_old_entries(self) -> None:
        """Remove oldest entries when cache is full."""
        if not self._cache:
            return

        # Sort by timestamp and remove oldest 20%
        sorted_items = sorted(self._cache.items(), key=lambda x: x[1][1])
        remove_count = max(1, len(sorted_items) // 5)

        for key, _ in sorted_items[:remove_count]:
            del self._cache[key]

    async def clear(self) -> None:
        """Clear all cached entries."""
        async with self._lock:
            self._cache.clear()


class BaseQueryService(ABC):
    """Base class for query services with common functionality."""

    def __init__(self, cache_ttl: int = 300):
        self.cache = CacheManager(default_ttl=cache_ttl)

    async def _execute_query(
        self,
        query: str,
        params: List[Any] = None,
        fetch_type: str = "fetch"
    ) -> Optional[Union[List[asyncpg.Record], asyncpg.Record, Any]]:
        """
        Execute database query with error handling.

        Args:
            query: SQL query string
            params: Query parameters
            fetch_type: Type of fetch operation ('fetch', 'fetchrow', 'fetchval')

        Returns:
            Query results or None if error occurred
        """
        if params is None:
            params = []

        try:
            async with get_connection() as conn:
                if fetch_type == "fetch":
                    return await conn.fetch(query, *params)
                elif fetch_type == "fetchrow":
                    return await conn.fetchrow(query, *params)
                elif fetch_type == "fetchval":
                    return await conn.fetchval(query, *params)
                else:
                    raise ValueError(f"Invalid fetch_type: {fetch_type}")

        except (asyncpg.PostgresError) as e:
            logger.error(f"Database error in {self.__class__.__name__}: {e}")
            return None
        except Exception as ex:
            logger.error(f"Unexpected error in {self.__class__.__name__}: {ex}")
            return None

    async def _cached_query(
        self,
        cache_key: str,
        query: str,
        params: List[Any] = None,
        fetch_type: str = "fetch",
        ttl: Optional[int] = None
    ) -> Optional[Union[List[asyncpg.Record], asyncpg.Record, Any]]:
        """
        Execute query with caching support.

        Args:
            cache_key: Unique cache key
            query: SQL query string
            params: Query parameters
            fetch_type: Type of fetch operation
            ttl: Custom TTL for this cache entry

        Returns:
            Cached or fresh query results
        """
        # Try to get from cache first
        cached_result = await self.cache.get(cache_key)
        if cached_result is not None:
            logger.debug(f"Cache hit for key: {cache_key[:16]}...")
            return cached_result

        # Execute query and cache result
        result = await self._execute_query(query, params, fetch_type)
        if result is not None:
            await self.cache.set(cache_key, result, ttl)
            logger.debug(f"Cache set for key: {cache_key[:16]}...")

        return result

    def _format_datetime_range(
        self,
        start_time_str: str,
        end_time_str: str
    ) -> Tuple[Optional[datetime], Optional[datetime]]:
        """
        Parse and format datetime range strings.

        Args:
            start_time_str: Start time string (YYYY-MM-DD)
            end_time_str: End time string (YYYY-MM-DD)

        Returns:
            Tuple of parsed datetime objects or (None, None) if parsing fails
        """
        try:
            start_time_dt = datetime.strptime(start_time_str, "%Y-%m-%d")
            end_time_dt = datetime.strptime(end_time_str, "%Y-%m-%d") + timedelta(days=1)
            return start_time_dt, end_time_dt
        except ValueError:
            logger.error(f"Invalid date format: {start_time_str} or {end_time_str}")
            return None, None

    def _safe_int(self, value: Any) -> int:
        """Safely convert value to integer."""
        try:
            return int(value) if value is not None else 0
        except (ValueError, TypeError):
            return 0

    def _safe_float(self, value: Any) -> float:
        """Safely convert value to float."""
        try:
            return float(value) if value is not None else 0.0
        except (ValueError, TypeError):
            return 0.0


class QueryBuilder:
    """Helper class for building optimized SQL queries."""

    @staticmethod
    def build_time_condition(
        time_column: str,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None,
        param_offset: int = 1
    ) -> Tuple[str, List[Any]]:
        """
        Build time-based WHERE conditions.

        Args:
            time_column: Name of the datetime column
            start_time: Optional start time filter
            end_time: Optional end time filter
            param_offset: Starting parameter number for SQL placeholders

        Returns:
            Tuple of (condition_string, parameters_list)
        """
        conditions = []
        params = []
        current_param = param_offset

        if start_time:
            conditions.append(f"{time_column} >= ${current_param}")
            params.append(start_time)
            current_param += 1

        if end_time:
            conditions.append(f"{time_column} < ${current_param}")
            params.append(end_time)
            current_param += 1

        condition_str = " AND ".join(conditions)
        return condition_str, params

    @staticmethod
    def build_pagination(
        limit: Optional[int] = None,
        offset: Optional[int] = None,
        param_offset: int = 1
    ) -> Tuple[str, List[Any]]:
        """
        Build LIMIT and OFFSET clauses.

        Args:
            limit: Maximum number of results
            offset: Number of results to skip
            param_offset: Starting parameter number for SQL placeholders

        Returns:
            Tuple of (pagination_string, parameters_list)
        """
        clauses = []
        params = []
        current_param = param_offset

        if limit is not None:
            clauses.append(f"LIMIT ${current_param}")
            params.append(limit)
            current_param += 1

        if offset is not None:
            clauses.append(f"OFFSET ${current_param}")
            params.append(offset)

        pagination_str = " ".join(clauses)
        return pagination_str, params


class PerformanceOptimizer:
    """Utilities for query performance optimization."""

    @staticmethod
    def suggest_indexes(table_name: str, query_patterns: List[Dict[str, Any]]) -> List[str]:
        """
        Suggest database indexes based on query patterns.

        Args:
            table_name: Name of the table
            query_patterns: List of query pattern dictionaries

        Returns:
            List of CREATE INDEX statements
        """
        indexes = []

        for pattern in query_patterns:
            columns = pattern.get('columns', [])
            order_by = pattern.get('order_by', [])
            where_columns = pattern.get('where_columns', [])

            # Create composite indexes for WHERE + ORDER BY patterns
            if where_columns and order_by:
                index_columns = where_columns + [col for col in order_by if col not in where_columns]
                index_name = f"idx_{table_name}_{'_'.join(index_columns)}"
                index_sql = f"CREATE INDEX IF NOT EXISTS {index_name} ON {table_name} ({', '.join(index_columns)});"
                indexes.append(index_sql)

        return indexes


# Global cache instances for different query types
user_stats_cache = CacheManager(default_ttl=300)  # 5 minutes
content_cache = CacheManager(default_ttl=600)     # 10 minutes
analytics_cache = CacheManager(default_ttl=1800)  # 30 minutes
