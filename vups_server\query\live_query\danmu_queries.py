"""
Dan<PERSON> (弹幕) query module for live streaming data.
Provides optimized queries for danmu data with caching and performance monitoring.
"""

from datetime import datetime
from typing import List, Tuple, Optional

from vups.logger import logger
from .base import LiveQueryBase
from .utils import LiveQueryUtils
from vups_server.sql.query_templates import LiveQueryTemplates, DANMU_COLUMNS


class DanmuQueries(LiveQueryBase):
    """
    Service class for danmu (弹幕) related queries.
    
    Provides optimized database queries for danmu data including:
    - Time-based range queries
    - Room-specific queries
    - Intelligent caching
    - Performance monitoring
    """
    
    def __init__(self, cache_ttl: int = 30):
        """
        Initialize DanmuQueries service.
        
        Args:
            cache_ttl: Cache TTL in seconds (default: 30 seconds for danmu data)
        """
        super().__init__(cache_ttl)
        self._query_templates = LiveQueryTemplates()
    
    async def query_by_room_and_timespan(
        self,
        room_id: str,
        start_ts: int,
        end_ts: int,
        use_cache: bool = True
    ) -> Tuple[List, int]:
        """
        Query danmu data by room ID and timestamp range.
        
        Args:
            room_id: Room ID
            start_ts: Start timestamp (milliseconds)
            end_ts: End timestamp (milliseconds)
            use_cache: Whether to use caching
            
        Returns:
            Tuple of (danmu_records, count)
        """
        try:
            # Validate inputs
            room_id_str = LiveQueryUtils.validate_room_id(room_id)
            start_ts, end_ts = LiveQueryUtils.validate_time_range(start_ts, end_ts, "timestamp")
            
            # Get table name and check existence
            table_name = await self._get_danmu_table_name_safe(room_id_str)
            if not table_name:
                logger.info(f"Danmu table for room {room_id_str} does not exist")
                return [], 0
            
            # Build cache key
            cache_key = None
            if use_cache:
                cache_key = self._create_cache_key(
                    "danmu_timespan", room_id_str, start_ts, end_ts
                )
            
            # Build optimized query
            query = self._query_templates.build_time_range_query(
                table_name=f'"{table_name}"',
                columns=DANMU_COLUMNS,
                time_field="timestamp",
                order_by="timestamp"
            )
            
            # Execute query
            results = await self._execute_live_query(
                query=query,
                params=[start_ts, end_ts],
                fetch_type="fetch",
                cache_key=cache_key,
                cache_ttl=self.cache._default_ttl,
                performance_key="danmu_timespan"
            )
            
            return self._format_query_result(results)
            
        except ValueError as e:
            logger.error(f"Validation error in danmu timespan query: {e}")
            return [], 0
        except Exception as e:
            logger.error(f"Error querying danmu by timespan: {e}")
            return [], 0
    
    async def query_by_room_and_datetime(
        self,
        room_id: str,
        start_datetime: datetime,
        end_datetime: datetime,
        use_cache: bool = True
    ) -> Tuple[List, int]:
        """
        Query danmu data by room ID and datetime range.
        
        Args:
            room_id: Room ID
            start_datetime: Start datetime
            end_datetime: End datetime
            use_cache: Whether to use caching
            
        Returns:
            Tuple of (danmu_records, count)
        """
        try:
            # Validate inputs
            room_id_str = LiveQueryUtils.validate_room_id(room_id)
            start_dt, end_dt = LiveQueryUtils.validate_time_range(
                start_datetime, end_datetime, "datetime"
            )
            
            # Get table name and check existence
            table_name = await self._get_danmu_table_name_safe(room_id_str)
            if not table_name:
                logger.info(f"Danmu table for room {room_id_str} does not exist")
                return [], 0
            
            # Build cache key
            cache_key = None
            if use_cache:
                cache_key = self._create_cache_key(
                    "danmu_datetime", room_id_str, start_dt.isoformat(), end_dt.isoformat()
                )
            
            # Build optimized query
            query = self._query_templates.build_time_range_query(
                table_name=f'"{table_name}"',
                columns=DANMU_COLUMNS,
                time_field="datetime",
                order_by="datetime"
            )
            
            # Execute query
            results = await self._execute_live_query(
                query=query,
                params=[start_dt, end_dt],
                fetch_type="fetch",
                cache_key=cache_key,
                cache_ttl=self.cache._default_ttl,
                performance_key="danmu_datetime"
            )
            
            return self._format_query_result(results)
            
        except ValueError as e:
            logger.error(f"Validation error in danmu datetime query: {e}")
            return [], 0
        except Exception as e:
            logger.error(f"Error querying danmu by datetime: {e}")
            return [], 0
    
    async def get_danmu_count_by_room_and_timespan(
        self,
        room_id: str,
        start_ts: int,
        end_ts: int,
        use_cache: bool = True
    ) -> int:
        """
        Get danmu count by room ID and timestamp range (optimized count query).
        
        Args:
            room_id: Room ID
            start_ts: Start timestamp (milliseconds)
            end_ts: End timestamp (milliseconds)
            use_cache: Whether to use caching
            
        Returns:
            Count of danmu records
        """
        try:
            # Validate inputs
            room_id_str = LiveQueryUtils.validate_room_id(room_id)
            start_ts, end_ts = LiveQueryUtils.validate_time_range(start_ts, end_ts, "timestamp")
            
            # Get table name and check existence
            table_name = await self._get_danmu_table_name_safe(room_id_str)
            if not table_name:
                return 0
            
            # Build cache key
            cache_key = None
            if use_cache:
                cache_key = self._create_cache_key(
                    "danmu_count_timespan", room_id_str, start_ts, end_ts
                )
            
            # Build count query
            query = f"""
                SELECT COUNT(*) as count
                FROM "{table_name}"
                WHERE timestamp BETWEEN $1 AND $2
            """
            
            # Execute query
            result = await self._execute_live_query(
                query=query,
                params=[start_ts, end_ts],
                fetch_type="fetchval",
                cache_key=cache_key,
                cache_ttl=self.cache._default_ttl,
                performance_key="danmu_count"
            )
            
            return LiveQueryUtils.safe_int(result, 0)
            
        except ValueError as e:
            logger.error(f"Validation error in danmu count query: {e}")
            return 0
        except Exception as e:
            logger.error(f"Error getting danmu count: {e}")
            return 0
    
    async def get_recent_danmu_by_room(
        self,
        room_id: str,
        limit: int = 100,
        use_cache: bool = True
    ) -> Tuple[List, int]:
        """
        Get recent danmu messages for a room.
        
        Args:
            room_id: Room ID
            limit: Maximum number of records to return
            use_cache: Whether to use caching
            
        Returns:
            Tuple of (recent_danmu_records, count)
        """
        try:
            # Validate inputs
            room_id_str = LiveQueryUtils.validate_room_id(room_id)
            limit = max(1, min(limit, 1000))  # Limit between 1 and 1000
            
            # Get table name and check existence
            table_name = await self._get_danmu_table_name_safe(room_id_str)
            if not table_name:
                logger.info(f"Danmu table for room {room_id_str} does not exist")
                return [], 0
            
            # Build cache key
            cache_key = None
            if use_cache:
                cache_key = self._create_cache_key(
                    "danmu_recent", room_id_str, limit
                )
            
            # Build query for recent danmu
            query = f"""
                SELECT {', '.join(DANMU_COLUMNS)}
                FROM "{table_name}"
                ORDER BY timestamp DESC
                LIMIT $1
            """
            
            # Execute query
            results = await self._execute_live_query(
                query=query,
                params=[limit],
                fetch_type="fetch",
                cache_key=cache_key,
                cache_ttl=30,  # Shorter cache for recent data
                performance_key="danmu_recent"
            )
            
            return self._format_query_result(results)
            
        except ValueError as e:
            logger.error(f"Validation error in recent danmu query: {e}")
            return [], 0
        except Exception as e:
            logger.error(f"Error getting recent danmu: {e}")
            return [], 0
    
    def get_performance_summary(self) -> dict:
        """
        Get performance summary for danmu queries.
        
        Returns:
            Dictionary with performance statistics
        """
        return {
            "danmu_timespan": self.get_performance_stats("danmu_timespan"),
            "danmu_datetime": self.get_performance_stats("danmu_datetime"),
            "danmu_count": self.get_performance_stats("danmu_count"),
            "danmu_recent": self.get_performance_stats("danmu_recent")
        }
