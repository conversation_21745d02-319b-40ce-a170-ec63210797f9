import asyncio
import time
from vups.algos.tools.video_dump import video_source_downloader
from vups.logger import logger


async def test_download_video(bvid: str = "BV1NXaJzhEFD", output_filename: str = "tests/vups/data/test_video.mp4") -> None:
    """Main function to download a Bilibili video.

    Args:
        bvid: Bilibili video ID to download
        output_filename: Output file name
    """
    try:
        await video_source_downloader.download_video(bvid=bvid, output_filename=output_filename)

    except Exception as e:
        logger.error(f"Failed to download video {bvid}: {e}")
        raise

async def test_download_audio(bvid: str = "BV1NXaJzhEFD", output_filename: str = "tests/vups/data/test_audio.mp3") -> None:
    """Main function to download a Bilibili video.

    Args:
        bvid: Bilibili video ID to download
        output_filename: Output file name
    """
    try:
        await video_source_downloader.download_audio(bvid=bvid, output_filename=output_filename)

    except Exception as e:
        logger.error(f"Failed to download audio {bvid}: {e}")
        raise

async def test_download_subtitle(bvid: str = "BV1NXaJzhEFD", output_filename: str = "tests/vups/data/test_subtitle.txt") -> None:

    try:
        await video_source_downloader.download_subtitle(bvid=bvid, output_filename=output_filename)

    except Exception as e:
        logger.error(f"Failed to download subtitle {bvid}: {e}")
        raise


if __name__ == "__main__":
    # asyncio.run(test_download_video()) # pass
    # asyncio.run(test_download_audio()) # pass
    # time
    time1 = time.time()
    asyncio.run(test_download_subtitle(bvid = "BV1wT9rYZENw")) # pass
    time2 = time.time()
    print(f"Time cost: {time2 - time1} secs")
