{"tests/vups/algos/tools/test_video_dump.py": true, "tests/test_query_vup_user_data_refactored.py::TestUserStatisticsFunctions::test_query_current_stat_by_mid_success": true, "tests/test_query_vup_user_data_refactored.py::TestUserStatisticsFunctions::test_query_current_stat_by_mid_none_result": true, "tests/test_query_vup_user_data_refactored.py::TestUserStatisticsFunctions::test_query_peroid_user_all_stat_by_uid_and_time": true, "tests/test_query_vup_user_data_refactored.py::TestUserStatisticsFunctions::test_query_whole_user_all_stat_by_uid_and_recent_default": true, "tests/test_query_vup_user_data_refactored.py::TestUserStatisticsFunctions::test_query_whole_user_all_stat_by_uid_and_recent_custom_days": true, "tests/test_query_vup_user_data_refactored.py::TestUserStatisticsFunctions::test_query_now_user_follower_num_by_mid": true, "tests/test_query_vup_user_data_refactored.py::TestUserStatisticsFunctions::test_query_now_user_follower_num_by_mid_error": true, "tests/test_query_vup_user_data_refactored.py::TestUserStatisticsFunctions::test_calculate_follower_rate_by_mid_default_days": true, "tests/test_query_vup_user_data_refactored.py::TestUserStatisticsFunctions::test_calculate_follower_rate_by_mid_custom_days": true, "tests/test_query_vup_user_data_refactored.py::TestUserStatisticsFunctions::test_query_current_follower_change_num_default": true, "tests/test_query_vup_user_data_refactored.py::TestUserStatisticsFunctions::test_query_current_follower_change_num_custom_days": true, "tests/test_query_vup_user_data_refactored.py::TestUserStatisticsFunctions::test_query_current_follower_change_num_none_result": true, "tests/test_query_vup_user_data_refactored.py::TestUserStatisticsFunctions::test_query_whole_user_follower_num_by_mid_and_recent_default": true, "tests/test_query_vup_user_data_refactored.py::TestUserStatisticsFunctions::test_query_whole_user_follower_num_by_mid_and_recent_custom_days": true, "tests/test_query_vup_user_data_refactored.py::TestUserStatisticsFunctions::test_query_whole_dahanghai_num_by_mid_and_recent_default": true, "tests/test_query_vup_user_data_refactored.py::TestUserStatisticsFunctions::test_query_whole_dahanghai_num_by_mid_and_recent_custom_days": true, "tests/test_query_vup_user_data_refactored.py::TestUserStatisticsFunctions::test_query_now_user_dahanghai_num_by_mid": true, "tests/test_query_vup_user_data_refactored.py::TestUserStatisticsFunctions::test_query_now_user_dahanghai_num_by_mid_error": true, "tests/test_query_vup_user_data_refactored.py::TestUserStatisticsFunctions::test_calculate_dahanghai_rate_by_mid_default_days": true, "tests/test_query_vup_user_data_refactored.py::TestUserStatisticsFunctions::test_calculate_dahanghai_rate_by_mid_custom_days": true, "tests/test_query_vup_user_data_refactored.py::TestUserStatisticsFunctions::test_query_current_dahanghai_change_num_default": true, "tests/test_query_vup_user_data_refactored.py::TestUserStatisticsFunctions::test_query_current_dahanghai_change_num_custom_days": true, "tests/test_query_vup_user_data_refactored.py::TestUserStatisticsFunctions::test_query_current_dahanghai_change_num_none_result": true, "tests/test_query_vup_user_data_refactored.py::TestContentQueryFunctions::test_query_user_info_by_mid_success": true, "tests/test_query_vup_user_data_refactored.py::TestContentQueryFunctions::test_query_user_info_by_mid_none_result": true, "tests/test_query_vup_user_data_refactored.py::TestContentQueryFunctions::test_query_user_dynamics_by_mid": true, "tests/test_query_vup_user_data_refactored.py::TestContentQueryFunctions::test_query_all_video_list_by_mid": true, "tests/test_query_vup_user_data_refactored.py::TestContentQueryFunctions::test_query_current_videos": true, "tests/test_query_vup_user_data_refactored.py::TestContentQueryFunctions::test_query_current_dynamics": true, "tests/test_query_vup_user_data_refactored.py::TestContentQueryFunctions::test_query_recent_top_n_videos_default_limit": true, "tests/test_query_vup_user_data_refactored.py::TestContentQueryFunctions::test_query_recent_top_n_videos_custom_limit": true, "tests/test_query_vup_user_data_refactored.py::TestContentQueryFunctions::test_query_comments_for_wordcloud_default_limit": true, "tests/test_query_vup_user_data_refactored.py::TestContentQueryFunctions::test_query_comments_for_wordcloud_custom_limit": true, "tests/test_query_vup_user_data_refactored.py::TestContentQueryFunctions::test_query_all_video_comments_by_mid": true, "tests/test_query_vup_user_data_refactored.py::TestContentQueryFunctions::test_query_all_dynamics_comments_by_mid": true, "tests/test_query_vup_user_data_refactored.py::TestAnalyticsFunctions::test_query_top_n_comments_default_limit": true, "tests/test_query_vup_user_data_refactored.py::TestAnalyticsFunctions::test_query_top_n_comments_custom_limit": true, "tests/test_query_vup_user_data_refactored.py::TestAnalyticsFunctions::test_query_top_n_comments_user_default_limit": true, "tests/test_query_vup_user_data_refactored.py::TestAnalyticsFunctions::test_query_top_n_videos_default_limit": true, "tests/test_query_vup_user_data_refactored.py::TestAnalyticsFunctions::test_query_top_n_dynamics_custom_limit": true, "tests/test_query_vup_user_data_refactored.py::TestAnalyticsFunctions::test_query_recent_relationships_default_limit": true, "tests/test_query_vup_user_data_refactored.py::TestAnalyticsFunctions::test_query_tieba_summaries_from_ai_gen_table_default_limit": true, "tests/test_query_vup_user_data_refactored.py::TestAnalyticsFunctions::test_query_rise_reason_from_ai_gen_table_custom_limit": true, "tests/test_query_vup_user_data_refactored.py::TestAnalyticsFunctions::test_query_latest_fans_medal_rank": true, "tests/test_query_vup_user_data_refactored.py::TestAnalyticsFunctions::test_query_fans_medal_rank_by_datetime": true, "tests/test_query_vup_user_data_refactored.py::TestAnalyticsFunctions::test_query_video_ai_conclusion_by_bvid_success": true, "tests/test_query_vup_user_data_refactored.py::TestAnalyticsFunctions::test_query_video_ai_conclusion_by_bvid_none_result": true, "tests/test_query_vup_user_data_refactored.py::TestAnalyticsFunctions::test_query_recent_comments_sentiment_value_default_days": true, "tests/test_query_vup_user_data_refactored.py::TestAnalyticsFunctions::test_query_recent_comments_sentiment_value_custom_days": true, "tests/test_query_vup_user_data_refactored.py::TestAnalyticsFunctions::test_query_recent_comments_sentiment_value_none_result": true, "tests/test_query_vup_user_data_refactored.py::TestSpecializedFunctions::test_query_tieba_whole": true, "tests/test_query_vup_user_data_refactored.py::TestSpecializedFunctions::test_query_tieba_threads_with_threads": true, "tests/test_query_vup_user_data_refactored.py::TestSpecializedFunctions::test_query_tieba_threads_no_threads": true, "tests/test_query_vup_user_data_refactored.py::TestSpecializedFunctions::test_query_latest_dahanghai_list_by_uid": true, "tests/test_query_vup_user_data_refactored.py::TestSpecializedFunctions::test_query_dahanghai_list_by_uid_and_datetime": true, "tests/test_query_vup_user_data_refactored.py::TestSpecializedFunctions::test_query_followers_list_no_datetime": true, "tests/test_query_vup_user_data_refactored.py::TestSpecializedFunctions::test_query_followers_list_with_datetime": true, "tests/test_query_vup_user_data_refactored.py::TestSpecializedFunctions::test_query_followers_review_list": true, "tests/test_query_vup_user_data_refactored.py::TestSpecializedFunctions::test_query_followers_review_rate_with_rate": true, "tests/test_query_vup_user_data_refactored.py::TestSpecializedFunctions::test_query_followers_review_rate_no_rate": true, "tests/test_query_vup_user_data_refactored.py::TestSpecializedFunctions::test_query_comment_wordcloud_default_limit": true, "tests/test_query_vup_user_data_refactored.py::TestSpecializedFunctions::test_query_comment_wordcloud_custom_limit": true, "tests/test_query_vup_user_data_refactored.py::TestSpecializedFunctions::test_query_comment_wordcloud_none_result": true, "tests/test_query_vup_user_data_refactored.py::TestSpecializedFunctions::test_cleanup_old_wordcloud_files": true, "tests/test_query_vup_user_data_refactored.py::TestSpecializedFunctions::test_query_recent_info": true, "tests/test_query_vup_user_data_refactored.py::TestSpecializedFunctions::test_query_recent_info_with_view_no_videos": true, "tests/test_query_vup_user_data_refactored.py::TestSpecializedFunctions::test_query_recent_info_with_view_with_videos": true, "tests/test_query_vup_user_data_refactored.py::TestSpecializedFunctions::test_query_recent_info_with_view_video_no_bvid": true, "tests/test_query_vup_user_data_refactored.py::TestParameterValidation::test_empty_uid_parameter": true, "tests/test_query_vup_user_data_refactored.py::TestParameterValidation::test_zero_limit_parameter": true, "tests/test_query_vup_user_data_refactored.py::TestParameterValidation::test_negative_limit_parameter": true, "tests/test_query_vup_user_data_refactored.py::TestParameterValidation::test_negative_recent_days_parameter": true}