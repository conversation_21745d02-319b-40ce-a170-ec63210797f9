"""
Gift and SuperChat query module for live streaming data.
Provides optimized queries for gift, superchat, and payment data with proper decimal handling.
"""

import time
from datetime import datetime
from decimal import Decimal, ROUND_HALF_UP
from typing import Dict, List, Tuple, Union, Any

from vups.logger import logger
from .base import LiveQueryBase
from .utils import LiveQueryUtils
from vups_server.sql.query_templates import LiveQ<PERSON>yTemplates, GIFT_COLUMNS, SUPERCHAT_COLUMNS


class GiftQueries(LiveQueryBase):
    """
    Service class for gift, superchat, and payment related queries.

    Provides optimized database queries for:
    - Gift data queries
    - SuperChat data queries
    - Payment calculations with proper decimal handling
    - Multi-table payment aggregations
    """

    def __init__(self, cache_ttl: int = 60):
        """
        Initialize GiftQueries service.

        Args:
            cache_ttl: Cache TTL in seconds (default: 60 seconds for gift data)
        """
        super().__init__(cache_ttl)
        self._query_templates = LiveQueryTemplates()

    async def query_superchat_by_room_and_timespan(
        self,
        room_id: str,
        start_ts: int,
        end_ts: int,
        use_cache: bool = True
    ) -> Tuple[List, int]:
        """
        Query SuperChat data by room ID and timestamp range.

        Args:
            room_id: Room ID
            start_ts: Start timestamp (milliseconds)
            end_ts: End timestamp (milliseconds)
            use_cache: Whether to use caching

        Returns:
            Tuple of (superchat_records, count)
        """
        try:
            # Validate inputs
            room_id_str = LiveQueryUtils.validate_room_id(room_id)
            start_ts, end_ts = LiveQueryUtils.validate_time_range(start_ts, end_ts, "timestamp")

            # Build cache key
            cache_key = None
            if use_cache:
                cache_key = self._create_cache_key(
                    "superchat_timespan", room_id_str, start_ts, end_ts
                )

            # Build optimized query
            query = self._query_templates.build_room_time_query(
                table_name="super_chat_table",
                columns=SUPERCHAT_COLUMNS,
                time_field="start_timestamp",
                order_by="start_timestamp"
            )

            # Execute query
            results = await self._execute_live_query(
                query=query,
                params=[room_id_str, start_ts, end_ts],
                fetch_type="fetch",
                cache_key=cache_key,
                cache_ttl=self.cache._default_ttl,
                performance_key="superchat_timespan"
            )

            return self._format_query_result(results)

        except ValueError as e:
            logger.error(f"Validation error in superchat timespan query: {e}")
            return [], 0
        except Exception as e:
            logger.error(f"Error querying superchat by timespan: {e}")
            return [], 0

    async def query_superchat_by_room_and_datetime(
        self,
        room_id: str,
        start_datetime: datetime,
        end_datetime: datetime,
        use_cache: bool = True
    ) -> Tuple[List, int]:
        """
        Query SuperChat data by room ID and datetime range.

        Args:
            room_id: Room ID
            start_datetime: Start datetime
            end_datetime: End datetime
            use_cache: Whether to use caching

        Returns:
            Tuple of (superchat_records, count)
        """
        try:
            # Validate inputs
            room_id_str = LiveQueryUtils.validate_room_id(room_id)
            start_dt, end_dt = LiveQueryUtils.validate_time_range(
                start_datetime, end_datetime, "datetime"
            )

            # Build cache key
            cache_key = None
            if use_cache:
                cache_key = self._create_cache_key(
                    "superchat_datetime", room_id_str, start_dt.isoformat(), end_dt.isoformat()
                )

            # Build optimized query
            query = self._query_templates.build_room_time_query(
                table_name="super_chat_table",
                columns=SUPERCHAT_COLUMNS,
                time_field="datetime",
                order_by="datetime"
            )

            # Execute query
            results = await self._execute_live_query(
                query=query,
                params=[room_id_str, start_dt, end_dt],
                fetch_type="fetch",
                cache_key=cache_key,
                cache_ttl=self.cache._default_ttl,
                performance_key="superchat_datetime"
            )

            return self._format_query_result(results)

        except ValueError as e:
            logger.error(f"Validation error in superchat datetime query: {e}")
            return [], 0
        except Exception as e:
            logger.error(f"Error querying superchat by datetime: {e}")
            return [], 0

    async def query_gift_by_room_and_timespan(
        self,
        room_id: str,
        start_ts: int,
        end_ts: int,
        use_cache: bool = True
    ) -> Tuple[List, int]:
        """
        Query gift data by room ID and timestamp range.

        Args:
            room_id: Room ID
            start_ts: Start timestamp (milliseconds)
            end_ts: End timestamp (milliseconds)
            use_cache: Whether to use caching

        Returns:
            Tuple of (gift_records, count)
        """
        try:
            # Validate inputs
            room_id_str = LiveQueryUtils.validate_room_id(room_id)
            start_ts, end_ts = LiveQueryUtils.validate_time_range(start_ts, end_ts, "timestamp")

            # Build cache key
            cache_key = None
            if use_cache:
                cache_key = self._create_cache_key(
                    "gift_timespan", room_id_str, start_ts, end_ts
                )

            # Build optimized query
            query = self._query_templates.build_room_time_query(
                table_name="gift_table",
                columns=GIFT_COLUMNS,
                time_field="timestamp",
                order_by="timestamp"
            )

            # Execute query
            results = await self._execute_live_query(
                query=query,
                params=[room_id_str, start_ts, end_ts],
                fetch_type="fetch",
                cache_key=cache_key,
                cache_ttl=self.cache._default_ttl,
                performance_key="gift_timespan"
            )

            return self._format_query_result(results)

        except ValueError as e:
            logger.error(f"Validation error in gift timespan query: {e}")
            return [], 0
        except Exception as e:
            logger.error(f"Error querying gift by timespan: {e}")
            return [], 0

    async def query_gift_by_room_and_datetime(
        self,
        room_id: str,
        start_datetime: datetime,
        end_datetime: datetime,
        use_cache: bool = True
    ) -> Tuple[List, int]:
        """
        Query gift data by room ID and datetime range.

        Args:
            room_id: Room ID
            start_datetime: Start datetime
            end_datetime: End datetime
            use_cache: Whether to use caching

        Returns:
            Tuple of (gift_records, count)
        """
        try:
            # Validate inputs
            room_id_str = LiveQueryUtils.validate_room_id(room_id)
            start_dt, end_dt = LiveQueryUtils.validate_time_range(
                start_datetime, end_datetime, "datetime"
            )

            # Build cache key
            cache_key = None
            if use_cache:
                cache_key = self._create_cache_key(
                    "gift_datetime", room_id_str, start_dt.isoformat(), end_dt.isoformat()
                )

            # Build optimized query
            query = self._query_templates.build_room_time_query(
                table_name="gift_table",
                columns=GIFT_COLUMNS,
                time_field="datetime",
                order_by="datetime"
            )

            # Execute query
            results = await self._execute_live_query(
                query=query,
                params=[room_id_str, start_dt, end_dt],
                fetch_type="fetch",
                cache_key=cache_key,
                cache_ttl=self.cache._default_ttl,
                performance_key="gift_datetime"
            )

            return self._format_query_result(results)

        except ValueError as e:
            logger.error(f"Validation error in gift datetime query: {e}")
            return [], 0
        except Exception as e:
            logger.error(f"Error querying gift by datetime: {e}")
            return [], 0

    async def query_pay_count_by_room_and_live_date(
        self,
        room_id: str,
        live_date: str,
        use_cache: bool = True
    ) -> Tuple[int, Decimal, Dict, float]:
        """
        Query payment count and income by room ID and live date.

        Args:
            room_id: Room ID
            live_date: Live date in YYYY-MM-DD format
            use_cache: Whether to use caching

        Returns:
            Tuple of (total_count, total_income, result_dict, execution_time_ms)
        """
        start_time = time.time()
        result = {"gift_table": [], "buy_guard_table": [], "super_chat_table": []}
        total_count = 0
        total_income = Decimal("0.0")

        try:
            # Validate inputs
            room_id_str = LiveQueryUtils.validate_room_id(room_id)
            formatted_date = LiveQueryUtils.format_live_date(live_date)

            # Build cache key
            cache_key = None
            if use_cache:
                cache_key = self._create_cache_key(
                    "pay_count_date", room_id_str, formatted_date
                )

                # Check cache first
                cached_result = await self.cache.get(cache_key)
                if cached_result is not None:
                    return cached_result

            # Query each payment table
            for table_name in LiveQueryUtils.PAYMENT_TABLES:
                query = f"""
                    SELECT id, room_id, live_id, user_id, user_name,
                           gift_per_price, gift_num, total_coin, price, datetime
                    FROM {table_name}
                    WHERE room_id = $1
                      AND datetime >= $2::DATE
                      AND datetime < $2::DATE + INTERVAL '1 DAY'
                """

                rows = await self._execute_query(query, [room_id_str, formatted_date], "fetch")
                if rows:
                    result[table_name] = list(rows)
                    total_count += len(rows)

                    # Calculate income based on table type
                    if table_name == "gift_table":
                        income = sum(
                            (Decimal(str(row["total_coin"] or 0)) / Decimal(1000)).quantize(
                                Decimal("0.0"), rounding=ROUND_HALF_UP
                            )
                            for row in rows
                        )
                    elif table_name == "buy_guard_table":
                        income = sum(
                            (
                                Decimal(str(row["gift_per_price"] or 0)) *
                                Decimal(str(row["gift_num"] or 0)) /
                                Decimal(1000)
                            ).quantize(Decimal("0.0"), rounding=ROUND_HALF_UP)
                            for row in rows
                        )
                    elif table_name == "super_chat_table":
                        income = sum(
                            Decimal(str(round((row["price"] or 0) * (row["gift_num"] or 0), 1)))
                            for row in rows
                        )
                    else:
                        income = Decimal("0.0")

                    total_income += income

            elapsed_ms = (time.time() - start_time) * 1000
            final_result = (total_count, total_income, result, elapsed_ms)

            # Cache the result
            if cache_key:
                await self.cache.set(cache_key, final_result)

            return final_result

        except ValueError as e:
            logger.error(f"Validation error in payment count query: {e}")
            return 0, Decimal("0.0"), {}, 0
        except Exception as e:
            logger.error(f"Error querying payment count by date: {e}")
            return 0, Decimal("0.0"), {}, 0

    async def query_pay_count_by_room_and_live_start_end_time(
        self,
        room_id: str,
        in_start_time: Union[str, int],
        in_end_time: Union[str, int],
        use_cache: bool = True
    ) -> Tuple[int, Decimal, Dict, float]:
        """
        Query payment count and income by room ID and time range.

        Args:
            room_id: Room ID
            in_start_time: Start time (string or timestamp)
            in_end_time: End time (string or timestamp)
            use_cache: Whether to use caching

        Returns:
            Tuple of (total_count, total_income, result_dict, execution_time_ms)
        """
        start_time = time.time()
        result = {"gift_table": [], "buy_guard_table": [], "super_chat_table": []}
        total_count = 0
        total_income = Decimal("0.0")

        try:
            # Validate inputs
            room_id_str = LiveQueryUtils.validate_room_id(room_id)

            # Build cache key
            cache_key = None
            if use_cache:
                cache_key = self._create_cache_key(
                    "pay_count_time_range", room_id_str, str(in_start_time), str(in_end_time)
                )

                # Check cache first
                cached_result = await self.cache.get(cache_key)
                if cached_result is not None:
                    return cached_result

            # Determine time field and parameters based on input type
            if isinstance(in_start_time, str) and isinstance(in_end_time, str):
                start_dt = datetime.strptime(in_start_time, "%Y-%m-%d %H:%M:%S")
                end_dt = datetime.strptime(in_end_time, "%Y-%m-%d %H:%M:%S")
                time_params = [room_id_str, start_dt, end_dt]
                time_field = "datetime"
            else:
                time_params = [room_id_str, in_start_time, in_end_time]
                time_field = "timestamp"  # Default for most tables

            # Query each payment table
            for table_name in LiveQueryUtils.PAYMENT_TABLES:
                # Use appropriate time field for each table
                if table_name == "super_chat_table" and time_field == "timestamp":
                    query_time_field = "start_timestamp"
                else:
                    query_time_field = time_field

                query = f"""
                    SELECT id, room_id, live_id, user_id, user_name,
                           gift_per_price, gift_num, total_coin, price, {time_field}
                    FROM {table_name}
                    WHERE room_id = $1 AND {query_time_field} BETWEEN $2 AND $3
                """

                rows = await self._execute_query(query, time_params, "fetch")
                if rows:
                    result[table_name] = list(rows)
                    total_count += len(rows)

                    # Calculate income (same logic as date query)
                    total_income += LiveQueryUtils.calculate_income_from_gifts(rows)

            elapsed_ms = (time.time() - start_time) * 1000
            final_result = (total_count, total_income, result, elapsed_ms)

            # Cache the result
            if cache_key:
                await self.cache.set(cache_key, final_result)

            return final_result

        except ValueError as e:
            logger.error(f"Validation error in payment time range query: {e}")
            return 0, Decimal("0.0"), {}, 0
        except Exception as e:
            logger.error(f"Error querying payment count by time range: {e}")
            return 0, Decimal("0.0"), {}, 0

    def get_performance_summary(self) -> dict:
        """
        Get performance summary for gift queries.

        Returns:
            Dictionary with performance statistics
        """
        return {
            "superchat_timespan": self.get_performance_stats("superchat_timespan"),
            "superchat_datetime": self.get_performance_stats("superchat_datetime"),
            "gift_timespan": self.get_performance_stats("gift_timespan"),
            "gift_datetime": self.get_performance_stats("gift_datetime")
        }
