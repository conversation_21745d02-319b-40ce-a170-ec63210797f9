"""
Base class for live streaming data queries.
Extends BaseQueryService with live-specific functionality and caching strategies.
"""

import time
from datetime import datetime
from typing import Any, Dict, List, Optional, Tuple, Union

import asyncpg
from vups.logger import logger
from vups_server.base.query_base import BaseQueryService
from vups_server.sql.sentence.live_sql import get_danmu_table_name


class LiveQueryBase(BaseQueryService):
    """
    Base class for live streaming data queries.
    
    Provides common functionality for all live data query services including:
    - Live-specific caching strategies
    - Table existence checking with caching
    - Common error handling patterns
    - Performance monitoring
    - Time conversion utilities
    """
    
    def __init__(self, cache_ttl: int = 300):
        """
        Initialize LiveQueryBase.
        
        Args:
            cache_ttl: Default cache TTL in seconds
        """
        super().__init__(cache_ttl)
        self._table_existence_cache: Dict[str, bool] = {}
        self._performance_stats: Dict[str, List[float]] = {}
    
    async def _check_table_exists(self, table_name: str) -> bool:
        """
        Check if a table exists with caching.
        
        Args:
            table_name: Name of the table to check
            
        Returns:
            True if table exists, False otherwise
        """
        # Check cache first
        if table_name in self._table_existence_cache:
            return self._table_existence_cache[table_name]
        
        try:
            exists = await self._execute_query(
                """
                SELECT EXISTS (
                    SELECT FROM information_schema.tables
                    WHERE table_schema = 'public'
                    AND table_name = $1
                );
                """,
                [table_name],
                "fetchval"
            )
            
            # Cache the result
            self._table_existence_cache[table_name] = bool(exists)
            return bool(exists)
            
        except Exception as e:
            logger.error(f"Error checking table existence for {table_name}: {e}")
            return False
    
    async def _execute_live_query(
        self,
        query: str,
        params: List[Any] = None,
        fetch_type: str = "fetch",
        cache_key: Optional[str] = None,
        cache_ttl: Optional[int] = None,
        performance_key: Optional[str] = None
    ) -> Optional[Union[List[asyncpg.Record], asyncpg.Record, Any]]:
        """
        Execute a live data query with performance monitoring and caching.
        
        Args:
            query: SQL query string
            params: Query parameters
            fetch_type: Type of fetch operation
            cache_key: Optional cache key for caching results
            cache_ttl: Optional custom cache TTL
            performance_key: Optional key for performance tracking
            
        Returns:
            Query results or None if error occurred
        """
        start_time = time.time()
        
        try:
            # Try cache first if cache_key provided
            if cache_key:
                cached_result = await self.cache.get(cache_key)
                if cached_result is not None:
                    logger.debug(f"Cache hit for live query: {cache_key[:16]}...")
                    return cached_result
            
            # Execute query
            result = await self._execute_query(query, params, fetch_type)
            
            # Cache result if cache_key provided
            if cache_key and result is not None:
                await self.cache.set(cache_key, result, cache_ttl)
            
            # Track performance
            if performance_key:
                execution_time = time.time() - start_time
                self._track_performance(performance_key, execution_time)
            
            return result
            
        except Exception as e:
            logger.error(f"Error executing live query: {e}")
            return None
    
    def _track_performance(self, key: str, execution_time: float) -> None:
        """
        Track query performance statistics.
        
        Args:
            key: Performance tracking key
            execution_time: Query execution time in seconds
        """
        if key not in self._performance_stats:
            self._performance_stats[key] = []
        
        self._performance_stats[key].append(execution_time)
        
        # Keep only last 100 measurements
        if len(self._performance_stats[key]) > 100:
            self._performance_stats[key] = self._performance_stats[key][-100:]
    
    def get_performance_stats(self, key: str) -> Dict[str, float]:
        """
        Get performance statistics for a query type.
        
        Args:
            key: Performance tracking key
            
        Returns:
            Dictionary with avg, min, max execution times
        """
        if key not in self._performance_stats or not self._performance_stats[key]:
            return {"avg": 0.0, "min": 0.0, "max": 0.0, "count": 0}
        
        times = self._performance_stats[key]
        return {
            "avg": sum(times) / len(times),
            "min": min(times),
            "max": max(times),
            "count": len(times)
        }
    
    def _create_cache_key(self, prefix: str, *args, **kwargs) -> str:
        """
        Create a cache key for live data queries.
        
        Args:
            prefix: Cache key prefix
            *args: Additional arguments for key generation
            **kwargs: Additional keyword arguments for key generation
            
        Returns:
            Generated cache key
        """
        key_parts = [prefix] + [str(arg) for arg in args]
        if kwargs:
            key_parts.extend([f"{k}={v}" for k, v in sorted(kwargs.items())])
        
        return self.cache._create_cache_key(*key_parts)
    
    def _validate_room_id(self, room_id: Any) -> str:
        """
        Validate and convert room_id to string.
        
        Args:
            room_id: Room ID to validate
            
        Returns:
            Validated room ID as string
            
        Raises:
            ValueError: If room_id is invalid
        """
        if room_id is None:
            raise ValueError("room_id cannot be None")
        
        room_id_str = str(room_id).strip()
        if not room_id_str:
            raise ValueError("room_id cannot be empty")
        
        return room_id_str
    
    def _validate_time_range(
        self,
        start_time: Any,
        end_time: Any
    ) -> Tuple[Any, Any]:
        """
        Validate time range parameters.
        
        Args:
            start_time: Start time
            end_time: End time
            
        Returns:
            Validated (start_time, end_time) tuple
            
        Raises:
            ValueError: If time range is invalid
        """
        if start_time is None or end_time is None:
            raise ValueError("start_time and end_time cannot be None")
        
        # For datetime objects, check that start < end
        if isinstance(start_time, datetime) and isinstance(end_time, datetime):
            if start_time >= end_time:
                raise ValueError("start_time must be before end_time")
        
        # For timestamps, check that start < end
        elif isinstance(start_time, (int, float)) and isinstance(end_time, (int, float)):
            if start_time >= end_time:
                raise ValueError("start_time must be before end_time")
        
        return start_time, end_time
    
    async def _get_danmu_table_name_safe(self, room_id: str) -> Optional[str]:
        """
        Get danmu table name and check if it exists.
        
        Args:
            room_id: Room ID
            
        Returns:
            Table name if exists, None otherwise
        """
        table_name = get_danmu_table_name(room_id)
        
        if await self._check_table_exists(table_name):
            return table_name
        else:
            logger.info(f"Danmu table {table_name} does not exist")
            return None
    
    def _format_query_result(
        self,
        results: Optional[List[asyncpg.Record]],
        default_count: int = 0
    ) -> Tuple[List, int]:
        """
        Format query results into standard tuple format.
        
        Args:
            results: Query results from database
            default_count: Default count if results is None
            
        Returns:
            Tuple of (results_list, count)
        """
        if results is None:
            return [], default_count
        
        return list(results), len(results)
    
    def clear_table_cache(self) -> None:
        """Clear the table existence cache."""
        self._table_existence_cache.clear()
        logger.info("Table existence cache cleared")
    
    def clear_performance_stats(self) -> None:
        """Clear performance statistics."""
        self._performance_stats.clear()
        logger.info("Performance statistics cleared")
