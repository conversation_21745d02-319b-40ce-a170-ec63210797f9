# Live Query Module Design Specification

## Module Structure

### 1. Base Classes and Utilities

#### LiveQueryBase (base.py)
- Extends BaseQueryService from query_base.py
- Provides live-specific caching strategies
- Common error handling for live data
- Table existence checking with caching
- Time conversion utilities

#### LiveQueryUtils (utils.py)
- Table name generation (danmu_table_{room_id})
- Time format conversion (timestamp ↔ datetime)
- Room ID validation
- Common SQL query builders

### 2. Specialized Query Modules

#### DanmuQueries (danmu_queries.py)
**Functions:**
- query_danmu_by_room_and_timespan()
- query_danmu_by_room_and_datetime()

**Optimizations:**
- Cached table existence checks
- Specific column selection
- Optimized time range queries

#### GiftQueries (gift_queries.py)
**Functions:**
- query_superchat_by_room_and_timespan()
- query_superchat_by_room_and_datetime()
- query_gift_by_room_and_timespan()
- query_gift_by_room_and_datetime()
- query_pay_count_by_room_and_live_date()
- query_pay_count_by_room_and_live_start_end_time()

**Optimizations:**
- Efficient payment calculations
- Proper Decimal handling
- Batch processing for multiple tables

#### StatisticsQueries (statistics_queries.py)
**Functions:**
- All count-based queries (enter_room, interact, active_watcher, online_rank)
- Aggregation functions (SUM, MAX, AVG)

**Optimizations:**
- Template-based query generation
- Efficient aggregation patterns
- Caching for frequently accessed counts

#### LiveStatusQueries (live_status_queries.py)
**Functions:**
- query_live_status_by_room_and_time()
- query_now_live_info_by_room()
- query_live_start_end_time_by_live_date()
- query_live_start_time_by_end_time()

**Optimizations:**
- Optimized time-based lookups
- Efficient session management
- Cross-day time handling

#### AnalyticsQueries (analytics_queries.py)
**Functions:**
- query_whole_live_info_with_live_id()
- query_minutes_live_info_with_live_id()
- query_live_info_with_room_id()

**Optimizations:**
- Efficient batch processing
- Multi-table join optimization
- Async data aggregation

### 3. Interface Design

#### Consistent Method Signatures
```python
async def query_by_room_and_timespan(room_id: str, start_ts: int, end_ts: int) -> Tuple[List, int]
async def query_by_room_and_datetime(room_id: str, start_dt: datetime, end_dt: datetime) -> Tuple[List, int]
async def query_by_room_and_time(room_id: str, ts: int) -> int
```

#### Caching Strategy
- Live status: 5-minute TTL
- Count statistics: 1-minute TTL  
- Session data: 10-minute TTL
- Danmu data: 30-second TTL

#### Error Handling
- Consistent exception handling across all modules
- Proper logging with context information
- Graceful degradation for missing tables

### 4. SQL Optimization Strategy

#### Required Indexes
```sql
-- Minute tables
CREATE INDEX idx_{table}_room_timestamp ON {table} (room_id, timestamp);
CREATE INDEX idx_{table}_room_datetime ON {table} (room_id, datetime);
CREATE INDEX idx_{table}_live_id ON {table} (live_id);

-- Gift and SuperChat tables
CREATE INDEX idx_gift_table_room_timestamp ON gift_table (room_id, timestamp);
CREATE INDEX idx_super_chat_table_room_start_ts ON super_chat_table (room_id, start_timestamp);

-- Session table
CREATE INDEX idx_live_session_room_datetime ON live_session_table (room_id, datetime DESC);
```

#### Query Templates
- Time range queries with proper column selection
- Aggregation queries with covering indexes
- Batch query patterns for multiple tables

### 5. Backward Compatibility

#### Compatibility Layer (query_live_info_data.py)
- Import all functions from new modules
- Maintain exact same function signatures
- Deprecation warnings for future migration

#### Migration Strategy
- Phase 1: Create new modules alongside existing code
- Phase 2: Implement compatibility layer
- Phase 3: Update internal usage to new modules
- Phase 4: Mark old functions as deprecated

### 6. Performance Targets

#### Query Performance Improvements
- 50-70% reduction in query execution time
- 80% reduction in data transfer (specific columns vs SELECT *)
- 90% cache hit rate for frequently accessed data

#### Code Maintainability
- 60% reduction in code duplication
- Consistent error handling across all functions
- Clear separation of concerns by functionality
