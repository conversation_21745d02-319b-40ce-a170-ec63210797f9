"""
Reusable SQL query templates and builders for live streaming data.
Provides common patterns for time-range queries, aggregations, and room-based filters.
"""

from typing import Dict, List, Tuple, Any
from datetime import datetime


class LiveQueryTemplates:
    """Template builder for common live data query patterns."""
    
    @staticmethod
    def build_time_range_query(
        table_name: str,
        columns: List[str],
        time_field: str = "timestamp",
        additional_conditions: str = "",
        order_by: str = None
    ) -> str:
        """
        Build a time-range query template.
        
        Args:
            table_name: Name of the table to query
            columns: List of columns to select
            time_field: Name of the time field (timestamp or datetime)
            additional_conditions: Additional WHERE conditions
            order_by: ORDER BY clause
        
        Returns:
            SQL query string with placeholders
        """
        columns_str = ", ".join(columns)
        
        query = f"""
            SELECT {columns_str}
            FROM {table_name}
            WHERE {time_field} BETWEEN $1 AND $2
        """
        
        if additional_conditions:
            query += f" AND {additional_conditions}"
        
        if order_by:
            query += f" ORDER BY {order_by}"
        else:
            query += f" ORDER BY {time_field}"
        
        return query.strip()
    
    @staticmethod
    def build_room_time_query(
        table_name: str,
        columns: List[str],
        time_field: str = "timestamp",
        additional_conditions: str = "",
        order_by: str = None
    ) -> str:
        """
        Build a room + time-range query template.
        
        Args:
            table_name: Name of the table to query
            columns: List of columns to select
            time_field: Name of the time field
            additional_conditions: Additional WHERE conditions
            order_by: ORDER BY clause
        
        Returns:
            SQL query string with placeholders ($1=room_id, $2=start_time, $3=end_time)
        """
        columns_str = ", ".join(columns)
        
        query = f"""
            SELECT {columns_str}
            FROM {table_name}
            WHERE room_id = $1 AND {time_field} BETWEEN $2 AND $3
        """
        
        if additional_conditions:
            query += f" AND {additional_conditions}"
        
        if order_by:
            query += f" ORDER BY {order_by}"
        else:
            query += f" ORDER BY {time_field}"
        
        return query.strip()
    
    @staticmethod
    def build_aggregation_query(
        table_name: str,
        aggregation: str,
        field: str,
        group_conditions: str = "",
        having_conditions: str = ""
    ) -> str:
        """
        Build an aggregation query template.
        
        Args:
            table_name: Name of the table to query
            aggregation: Aggregation function (SUM, COUNT, MAX, AVG)
            field: Field to aggregate
            group_conditions: WHERE conditions
            having_conditions: HAVING conditions
        
        Returns:
            SQL query string with placeholders
        """
        query = f"""
            SELECT COALESCE({aggregation}({field}), 0) as result
            FROM {table_name}
        """
        
        if group_conditions:
            query += f" WHERE {group_conditions}"
        
        if having_conditions:
            query += f" HAVING {having_conditions}"
        
        return query.strip()
    
    @staticmethod
    def build_room_aggregation_query(
        table_name: str,
        aggregation: str,
        field: str,
        time_field: str = "timestamp",
        additional_conditions: str = ""
    ) -> str:
        """
        Build a room-based aggregation query with time range.
        
        Args:
            table_name: Name of the table to query
            aggregation: Aggregation function
            field: Field to aggregate
            time_field: Time field name
            additional_conditions: Additional WHERE conditions
        
        Returns:
            SQL query string ($1=room_id, $2=start_time, $3=end_time)
        """
        query = f"""
            SELECT COALESCE({aggregation}({field}), 0) as result
            FROM {table_name}
            WHERE room_id = $1 AND {time_field} BETWEEN $2 AND $3
        """
        
        if additional_conditions:
            query += f" AND {additional_conditions}"
        
        return query.strip()
    
    @staticmethod
    def build_latest_record_query(
        table_name: str,
        columns: List[str],
        filter_conditions: str = "",
        order_field: str = "timestamp"
    ) -> str:
        """
        Build a query to get the latest record.
        
        Args:
            table_name: Name of the table to query
            columns: List of columns to select
            filter_conditions: WHERE conditions
            order_field: Field to order by for "latest"
        
        Returns:
            SQL query string with placeholders
        """
        columns_str = ", ".join(columns)
        
        query = f"""
            SELECT {columns_str}
            FROM {table_name}
        """
        
        if filter_conditions:
            query += f" WHERE {filter_conditions}"
        
        query += f" ORDER BY {order_field} DESC LIMIT 1"
        
        return query.strip()
    
    @staticmethod
    def build_union_payment_query(
        room_id_param: str = "$1",
        time_field: str = "datetime",
        start_time_param: str = "$2",
        end_time_param: str = "$3"
    ) -> str:
        """
        Build a UNION query for payment data across multiple tables.
        
        Args:
            room_id_param: Parameter placeholder for room_id
            time_field: Time field name (datetime or timestamp)
            start_time_param: Parameter placeholder for start time
            end_time_param: Parameter placeholder for end time
        
        Returns:
            SQL query string for unified payment data
        """
        return f"""
            SELECT 'gift_table' as source_table, 
                   total_coin, gift_per_price, gift_num, {time_field}
            FROM gift_table
            WHERE room_id = {room_id_param} 
              AND {time_field} BETWEEN {start_time_param} AND {end_time_param}
            
            UNION ALL
            
            SELECT 'buy_guard_table' as source_table,
                   0 as total_coin, gift_per_price, gift_num, {time_field}
            FROM buy_guard_table
            WHERE room_id = {room_id_param} 
              AND {time_field} BETWEEN {start_time_param} AND {end_time_param}
            
            UNION ALL
            
            SELECT 'super_chat_table' as source_table,
                   0 as total_coin, price as gift_per_price, gift_num, {time_field}
            FROM super_chat_table
            WHERE room_id = {room_id_param} 
              AND {time_field} BETWEEN {start_time_param} AND {end_time_param}
        """.strip()


class QueryParameterBuilder:
    """Helper class for building query parameters."""
    
    @staticmethod
    def build_time_params(
        room_id: str,
        start_time: Any,
        end_time: Any
    ) -> Tuple[str, Any, Any]:
        """
        Build parameters for room + time queries.
        
        Returns:
            Tuple of (room_id_str, start_time, end_time)
        """
        return str(room_id), start_time, end_time
    
    @staticmethod
    def build_single_time_params(
        room_id: str,
        timestamp: Any
    ) -> Tuple[str, Any]:
        """
        Build parameters for room + single timestamp queries.
        
        Returns:
            Tuple of (room_id_str, timestamp)
        """
        return str(room_id), timestamp


# Common column sets for different query types
DANMU_COLUMNS = [
    "id", "room_id", "live_id", "user_id", "user_name", "message",
    "medal_level", "medal_name", "timestamp", "datetime"
]

GIFT_COLUMNS = [
    "id", "room_id", "live_id", "user_id", "user_name", "gift_name",
    "gift_num", "gift_per_price", "total_coin", "timestamp", "datetime"
]

SUPERCHAT_COLUMNS = [
    "id", "room_id", "live_id", "user_id", "user_name", "price",
    "super_chat_msg", "gift_num", "start_timestamp", "datetime"
]

LIVE_STATUS_COLUMNS = [
    "id", "room_id", "live_id", "live_status", "live_action", "title",
    "cover", "parent_area", "area", "timestamp", "datetime"
]

LIVE_SESSION_COLUMNS = [
    "live_id", "danmu_count", "start_time_str", "end_time_str", "parent_area",
    "area", "cover", "title", "income", "watch_change_count",
    "like_info_update_count", "pay_count", "interaction_count",
    "max_online_rank", "enter_room_count", "ave_online_rank", "ave_enter_room"
]
