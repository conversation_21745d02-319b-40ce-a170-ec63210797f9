"""
Live Query Module - Optimized database queries for live streaming data.

This module provides high-performance, well-organized query functions for live streaming
data including danmu, gifts, superchat, statistics, and analytics.

Key Features:
- Optimized SQL queries with specific column selection
- Intelligent caching strategies
- Consistent error handling
- Modular organization by functionality
- Backward compatibility with existing API

Usage:
    from vups_server.query.live_query import DanmuQueries, GiftQueries
    
    danmu_service = DanmuQueries()
    results = await danmu_service.query_by_room_and_timespan(room_id, start_ts, end_ts)
"""

# Import all query services for easy access
from .base import LiveQueryBase
from .utils import LiveQueryUtils
from .danmu_queries import DanmuQueries
from .gift_queries import GiftQueries
from .statistics_queries import StatisticsQueries
from .live_status_queries import LiveStatusQueries
from .analytics_queries import AnalyticsQueries

# Public API - these are the main classes users should import
__all__ = [
    'LiveQueryBase',
    'LiveQueryUtils',
    'DanmuQueries',
    'GiftQueries', 
    'StatisticsQueries',
    'LiveStatusQueries',
    'AnalyticsQueries'
]

# Version information
__version__ = '1.0.0'
__author__ = 'VUPS Development Team'

# Default service instances for backward compatibility
_danmu_service = None
_gift_service = None
_statistics_service = None
_live_status_service = None
_analytics_service = None


def get_danmu_service() -> DanmuQueries:
    """Get singleton instance of DanmuQueries service."""
    global _danmu_service
    if _danmu_service is None:
        _danmu_service = DanmuQueries()
    return _danmu_service


def get_gift_service() -> GiftQueries:
    """Get singleton instance of GiftQueries service."""
    global _gift_service
    if _gift_service is None:
        _gift_service = GiftQueries()
    return _gift_service


def get_statistics_service() -> StatisticsQueries:
    """Get singleton instance of StatisticsQueries service."""
    global _statistics_service
    if _statistics_service is None:
        _statistics_service = StatisticsQueries()
    return _statistics_service


def get_live_status_service() -> LiveStatusQueries:
    """Get singleton instance of LiveStatusQueries service."""
    global _live_status_service
    if _live_status_service is None:
        _live_status_service = LiveStatusQueries()
    return _live_status_service


def get_analytics_service() -> AnalyticsQueries:
    """Get singleton instance of AnalyticsQueries service."""
    global _analytics_service
    if _analytics_service is None:
        _analytics_service = AnalyticsQueries()
    return _analytics_service


# Convenience functions for backward compatibility
async def query_danmu_by_room_and_timespan(room_id, start_ts, end_ts):
    """Backward compatibility function for danmu queries."""
    service = get_danmu_service()
    return await service.query_by_room_and_timespan(room_id, start_ts, end_ts)


async def query_danmu_by_room_and_datetime(room_id, start_datetime, end_datetime):
    """Backward compatibility function for danmu queries."""
    service = get_danmu_service()
    return await service.query_by_room_and_datetime(room_id, start_datetime, end_datetime)


# Module configuration
DEFAULT_CACHE_TTL = {
    'danmu': 30,        # 30 seconds for danmu data
    'gifts': 60,        # 1 minute for gift data
    'statistics': 60,   # 1 minute for statistics
    'live_status': 300, # 5 minutes for live status
    'analytics': 600    # 10 minutes for analytics
}

# Performance monitoring
QUERY_PERFORMANCE_LOG = True
CACHE_HIT_RATE_LOG = True
