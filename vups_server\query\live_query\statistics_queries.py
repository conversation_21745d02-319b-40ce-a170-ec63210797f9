"""
Statistics query module for live streaming data.
Provides optimized queries for count-based statistics with efficient aggregation patterns.
"""

from datetime import datetime
from typing import Union, Optional

from vups.logger import logger
from .base import LiveQueryBase
from .utils import LiveQueryUtils
from vups_server.sql.query_templates import LiveQueryTemplates


class StatisticsQueries(LiveQueryBase):
    """
    Service class for statistics and count-based queries.

    Provides optimized database queries for:
    - Enter room count statistics
    - Interaction count statistics
    - Active watcher count statistics
    - Online rank count statistics
    - Efficient aggregation operations (SUM, MAX, AVG)
    """

    def __init__(self, cache_ttl: int = 60):
        """
        Initialize StatisticsQueries service.

        Args:
            cache_ttl: Cache TTL in seconds (default: 60 seconds for statistics)
        """
        super().__init__(cache_ttl)
        self._query_templates = LiveQueryTemplates()

    async def query_enter_room_count_by_room_and_time(
        self,
        room_id: str,
        ts: int,
        use_cache: bool = True
    ) -> int:
        """
        Query enter room count by room ID and timestamp.

        Args:
            room_id: Room ID
            ts: Timestamp (milliseconds)
            use_cache: Whether to use caching

        Returns:
            Enter room count
        """
        return await self._query_count_by_room_and_time(
            room_id, ts, "enter_room", use_cache
        )

    async def query_interact_word_count_by_room_and_time(
        self,
        room_id: str,
        ts: int,
        use_cache: bool = True
    ) -> int:
        """
        Query interaction count by room ID and timestamp.

        Args:
            room_id: Room ID
            ts: Timestamp (milliseconds)
            use_cache: Whether to use caching

        Returns:
            Interaction count
        """
        return await self._query_count_by_room_and_time(
            room_id, ts, "interact", use_cache
        )

    async def query_active_watcher_count_by_room_and_time(
        self,
        room_id: str,
        ts: int,
        use_cache: bool = True
    ) -> int:
        """
        Query active watcher count by room ID and timestamp.

        Args:
            room_id: Room ID
            ts: Timestamp (milliseconds)
            use_cache: Whether to use caching

        Returns:
            Active watcher count
        """
        return await self._query_count_by_room_and_time(
            room_id, ts, "active_watcher", use_cache
        )

    async def query_online_rank_count_by_room_and_time(
        self,
        room_id: str,
        ts: int,
        use_cache: bool = True
    ) -> int:
        """
        Query online rank count by room ID and timestamp.

        Args:
            room_id: Room ID
            ts: Timestamp (milliseconds)
            use_cache: Whether to use caching

        Returns:
            Online rank count
        """
        return await self._query_count_by_room_and_time(
            room_id, ts, "online_rank", use_cache
        )

    async def _query_count_by_room_and_time(
        self,
        room_id: str,
        ts: int,
        count_type: str,
        use_cache: bool = True
    ) -> int:
        """
        Generic method for count queries by room and time.

        Args:
            room_id: Room ID
            ts: Timestamp
            count_type: Type of count ('enter_room', 'interact', 'active_watcher', 'online_rank')
            use_cache: Whether to use caching

        Returns:
            Count value
        """
        try:
            # Validate inputs
            room_id_str = LiveQueryUtils.validate_room_id(room_id)
            ts_int = LiveQueryUtils.validate_timestamp(ts)

            # Get table name
            table_name = LiveQueryUtils.get_table_name(count_type)

            # Build cache key
            cache_key = None
            if use_cache:
                cache_key = self._create_cache_key(
                    f"{count_type}_count_time", room_id_str, ts_int
                )

            # Build aggregation query
            query = self._query_templates.build_room_aggregation_query(
                table_name=table_name,
                aggregation="SUM",
                field="count",
                time_field="timestamp"
            )

            # Execute query
            result = await self._execute_live_query(
                query=query,
                params=[room_id_str, ts_int, ts_int],  # timestamp = ts_int
                fetch_type="fetchval",
                cache_key=cache_key,
                cache_ttl=self.cache._default_ttl,
                performance_key=f"{count_type}_count_time"
            )

            return LiveQueryUtils.safe_int(result, 0)

        except ValueError as e:
            logger.error(f"Validation error in {count_type} count query: {e}")
            return 0
        except Exception as e:
            logger.error(f"Error querying {count_type} count by time: {e}")
            return 0

    async def query_active_watcher_count_by_room_and_datetime(
        self,
        room_id: str,
        start_datetime: datetime,
        end_datetime: datetime,
        use_cache: bool = True
    ) -> int:
        """
        Query max active watcher count by room ID and datetime range.

        Args:
            room_id: Room ID
            start_datetime: Start datetime
            end_datetime: End datetime
            use_cache: Whether to use caching

        Returns:
            Maximum active watcher count
        """
        return await self._query_aggregation_by_room_and_datetime(
            room_id, start_datetime, end_datetime, "active_watcher", "MAX", use_cache
        )

    async def query_interact_word_count_by_room_and_datetime(
        self,
        room_id: str,
        start_datetime: datetime,
        end_datetime: datetime,
        use_cache: bool = True
    ) -> int:
        """
        Query total interaction count by room ID and datetime range.

        Args:
            room_id: Room ID
            start_datetime: Start datetime
            end_datetime: End datetime
            use_cache: Whether to use caching

        Returns:
            Total interaction count
        """
        return await self._query_aggregation_by_room_and_datetime(
            room_id, start_datetime, end_datetime, "interact", "SUM", use_cache
        )

    async def query_max_online_rank_count_by_room_and_datetime(
        self,
        room_id: str,
        start_datetime: datetime,
        end_datetime: datetime,
        use_cache: bool = True
    ) -> int:
        """
        Query max online rank count by room ID and datetime range.

        Args:
            room_id: Room ID
            start_datetime: Start datetime
            end_datetime: End datetime
            use_cache: Whether to use caching

        Returns:
            Maximum online rank count
        """
        return await self._query_aggregation_by_room_and_datetime(
            room_id, start_datetime, end_datetime, "online_rank", "MAX", use_cache
        )

    async def query_average_online_rank_count_by_room_and_datetime(
        self,
        room_id: str,
        start_datetime: datetime,
        end_datetime: datetime,
        use_cache: bool = True
    ) -> int:
        """
        Query average online rank count by room ID and datetime range.

        Args:
            room_id: Room ID
            start_datetime: Start datetime
            end_datetime: End datetime
            use_cache: Whether to use caching

        Returns:
            Average online rank count (rounded)
        """
        return await self._query_aggregation_by_room_and_datetime(
            room_id, start_datetime, end_datetime, "online_rank", "ROUND(AVG", use_cache
        )

    async def query_average_enter_room_count_by_room_and_datetime(
        self,
        room_id: str,
        start_datetime: datetime,
        end_datetime: datetime,
        use_cache: bool = True
    ) -> int:
        """
        Query average enter room count by room ID and datetime range.

        Args:
            room_id: Room ID
            start_datetime: Start datetime
            end_datetime: End datetime
            use_cache: Whether to use caching

        Returns:
            Average enter room count (rounded)
        """
        return await self._query_aggregation_by_room_and_datetime(
            room_id, start_datetime, end_datetime, "enter_room", "ROUND(AVG", use_cache
        )

    async def query_total_enter_room_count_by_room_and_datetime(
        self,
        room_id: str,
        start_datetime: datetime,
        end_datetime: datetime,
        use_cache: bool = True
    ) -> int:
        """
        Query total enter room count by room ID and datetime range.

        Args:
            room_id: Room ID
            start_datetime: Start datetime
            end_datetime: End datetime
            use_cache: Whether to use caching

        Returns:
            Total enter room count
        """
        return await self._query_aggregation_by_room_and_datetime(
            room_id, start_datetime, end_datetime, "enter_room", "SUM", use_cache
        )

    async def _query_aggregation_by_room_and_datetime(
        self,
        room_id: str,
        start_datetime: datetime,
        end_datetime: datetime,
        count_type: str,
        aggregation: str,
        use_cache: bool = True
    ) -> int:
        """
        Generic method for aggregation queries by room and datetime range.

        Args:
            room_id: Room ID
            start_datetime: Start datetime
            end_datetime: End datetime
            count_type: Type of count ('enter_room', 'interact', 'active_watcher', 'online_rank')
            aggregation: Aggregation function ('SUM', 'MAX', 'AVG', 'ROUND(AVG')
            use_cache: Whether to use caching

        Returns:
            Aggregated count value
        """
        try:
            # Validate inputs
            room_id_str = LiveQueryUtils.validate_room_id(room_id)
            start_dt, end_dt = LiveQueryUtils.validate_time_range(
                start_datetime, end_datetime, "datetime"
            )

            # Get table name
            table_name = LiveQueryUtils.get_table_name(count_type)

            # Build cache key
            cache_key = None
            if use_cache:
                cache_key = self._create_cache_key(
                    f"{count_type}_{aggregation.lower()}_datetime",
                    room_id_str,
                    start_dt.isoformat(),
                    end_dt.isoformat()
                )

            # Handle special aggregation functions
            if aggregation.startswith("ROUND(AVG"):
                agg_field = f"ROUND(AVG(count))"
            else:
                agg_field = f"{aggregation}(count)"

            # Build aggregation query
            query = f"""
                SELECT COALESCE({agg_field}, 0) as result
                FROM {table_name}
                WHERE room_id = $1 AND datetime BETWEEN $2 AND $3
            """

            # Execute query
            result = await self._execute_live_query(
                query=query,
                params=[room_id_str, start_dt, end_dt],
                fetch_type="fetchval",
                cache_key=cache_key,
                cache_ttl=self.cache._default_ttl,
                performance_key=f"{count_type}_{aggregation.lower()}_datetime"
            )

            return LiveQueryUtils.safe_int(result, 0)

        except ValueError as e:
            logger.error(f"Validation error in {count_type} {aggregation} query: {e}")
            return 0
        except Exception as e:
            logger.error(f"Error querying {count_type} {aggregation} by datetime: {e}")
            return 0

    async def get_statistics_summary_by_room_and_datetime(
        self,
        room_id: str,
        start_datetime: datetime,
        end_datetime: datetime,
        use_cache: bool = True
    ) -> dict:
        """
        Get comprehensive statistics summary for a room and time range.

        Args:
            room_id: Room ID
            start_datetime: Start datetime
            end_datetime: End datetime
            use_cache: Whether to use caching

        Returns:
            Dictionary with all statistics
        """
        try:
            # Build cache key for summary
            cache_key = None
            if use_cache:
                cache_key = self._create_cache_key(
                    "stats_summary",
                    room_id,
                    start_datetime.isoformat(),
                    end_datetime.isoformat()
                )

                # Check cache first
                cached_result = await self.cache.get(cache_key)
                if cached_result is not None:
                    return cached_result

            # Gather all statistics
            summary = {
                "total_enter_room": await self.query_total_enter_room_count_by_room_and_datetime(
                    room_id, start_datetime, end_datetime, use_cache=False
                ),
                "avg_enter_room": await self.query_average_enter_room_count_by_room_and_datetime(
                    room_id, start_datetime, end_datetime, use_cache=False
                ),
                "total_interactions": await self.query_interact_word_count_by_room_and_datetime(
                    room_id, start_datetime, end_datetime, use_cache=False
                ),
                "max_active_watchers": await self.query_active_watcher_count_by_room_and_datetime(
                    room_id, start_datetime, end_datetime, use_cache=False
                ),
                "max_online_rank": await self.query_max_online_rank_count_by_room_and_datetime(
                    room_id, start_datetime, end_datetime, use_cache=False
                ),
                "avg_online_rank": await self.query_average_online_rank_count_by_room_and_datetime(
                    room_id, start_datetime, end_datetime, use_cache=False
                )
            }

            # Cache the summary
            if cache_key:
                await self.cache.set(cache_key, summary)

            return summary

        except Exception as e:
            logger.error(f"Error getting statistics summary: {e}")
            return {
                "total_enter_room": 0,
                "avg_enter_room": 0,
                "total_interactions": 0,
                "max_active_watchers": 0,
                "max_online_rank": 0,
                "avg_online_rank": 0
            }

    def get_performance_summary(self) -> dict:
        """
        Get performance summary for statistics queries.

        Returns:
            Dictionary with performance statistics
        """
        return {
            "enter_room_count_time": self.get_performance_stats("enter_room_count_time"),
            "interact_count_time": self.get_performance_stats("interact_count_time"),
            "active_watcher_count_time": self.get_performance_stats("active_watcher_count_time"),
            "online_rank_count_time": self.get_performance_stats("online_rank_count_time"),
            "aggregation_queries": {
                stat_type: self.get_performance_stats(f"{stat_type}_datetime")
                for stat_type in ["enter_room", "interact", "active_watcher", "online_rank"]
            }
        }
